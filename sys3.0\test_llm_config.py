#!/usr/bin/env python
"""
测试LLM配置传递
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
ROOT_DIR = Path(__file__).parent
sys.path.insert(0, str(ROOT_DIR))

# 导入配置服务
from config.config import config_service

# 加载配置
config_service.load_config()

def test_config_loading():
    """测试配置加载"""
    print("=" * 50)
    print("测试配置加载...")
    
    # 获取LLM配置
    llm_config = config_service.get_config("llm", as_dict=True)
    
    print(f"LLM配置:")
    for key, value in llm_config.items():
        if 'key' in key.lower():
            print(f"  {key}: {value[:10]}..." if value else f"  {key}: {value}")
        else:
            print(f"  {key}: {value}")
    
    return llm_config

def test_llm_client_config():
    """测试LLMClient配置传递"""
    print("\n" + "=" * 50)
    print("测试LLMClient配置传递...")
    
    from tools.llm_client import LLMClient
    
    # 创建LLM客户端
    llm_client = LLMClient()
    
    print(f"LLMClient配置:")
    print(f"  provider: {llm_client.provider}")
    print(f"  model: {llm_client.model}")
    print(f"  api_base: {llm_client.api_base}")
    print(f"  timeout: {llm_client.timeout}")
    print(f"  max_retries: {llm_client.max_retries}")
    
    # 获取LLM实例
    llm = llm_client.get_llm()
    
    print(f"\nCrewAI LLM实例配置:")
    print(f"  model: {llm.model}")
    print(f"  base_url: {llm.base_url}")
    print(f"  api_base: {getattr(llm, 'api_base', 'N/A')}")
    print(f"  timeout: {llm.timeout}")
    
    return llm_client, llm

def test_direct_litellm():
    """测试直接LiteLLM调用"""
    print("\n" + "=" * 50)
    print("测试直接LiteLLM调用...")
    
    import litellm
    
    llm_config = config_service.get_config("llm", as_dict=True)
    
    # 设置litellm的详细日志
    litellm.set_verbose = True
    
    try:
        response = litellm.completion(
            model=f"openai/{llm_config.get('model')}",
            messages=[{"role": "user", "content": "测试"}],
            api_key=llm_config.get('api_key'),
            base_url=llm_config.get('base_url'),
            timeout=llm_config.get('timeout', 60)
        )
        
        print(f"✅ 直接LiteLLM调用成功")
        print(f"响应: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ 直接LiteLLM调用失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("LLM配置测试开始")
    print("=" * 50)
    
    # 测试配置加载
    config = test_config_loading()
    
    # 测试LLMClient配置传递
    client, llm = test_llm_client_config()
    
    # 测试直接LiteLLM调用
    direct_ok = test_direct_litellm()
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print(f"配置加载: ✅ 成功")
    print(f"LLMClient: ✅ 成功")
    print(f"直接调用: {'✅ 成功' if direct_ok else '❌ 失败'}")
    
    if direct_ok:
        print("\n🎉 LLM配置完全正确！")
    else:
        print("\n⚠️ LLM配置存在问题。")
