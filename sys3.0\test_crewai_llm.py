#!/usr/bin/env python
"""
CrewAI LLM测试
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
ROOT_DIR = Path(__file__).parent
sys.path.insert(0, str(ROOT_DIR))

# 导入配置服务
from config.config import config_service

# 加载配置
config_service.load_config()

def test_crewai_llm_init():
    """测试CrewAI LLM初始化"""
    print("=" * 50)
    print("测试CrewAI LLM初始化...")
    
    try:
        from crewai import LLM
        
        # 获取配置
        llm_config = config_service.get_config("llm", as_dict=True)
        
        print(f"配置信息:")
        print(f"  模型: {llm_config.get('model')}")
        print(f"  API Base: {llm_config.get('base_url')}")
        print(f"  超时: {llm_config.get('timeout')}秒")
        
        # 构建模型名称
        model_name = f"openai/{llm_config.get('model')}"
        
        print(f"\n创建CrewAI LLM实例...")
        print(f"模型名称: {model_name}")
        
        # 创建LLM实例
        llm = LLM(
            model=model_name,
            api_key=llm_config.get('api_key'),
            base_url=llm_config.get('base_url'),
            temperature=llm_config.get('temperature', 0.7),
            max_tokens=llm_config.get('max_tokens', 1000),
            timeout=llm_config.get('timeout', 60)
        )
        
        print(f"✅ CrewAI LLM初始化成功!")
        print(f"LLM对象: {llm}")
        print(f"可用方法: {[method for method in dir(llm) if not method.startswith('_')]}")
        
        return llm
        
    except Exception as e:
        print(f"❌ CrewAI LLM初始化失败: {str(e)}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return None

def test_crewai_llm_call(llm):
    """测试CrewAI LLM调用"""
    print("\n" + "=" * 50)
    print("测试CrewAI LLM调用...")
    
    if not llm:
        print("❌ LLM实例为空，跳过调用测试")
        return False
    
    try:
        print("开始调用...")

        # 使用threading实现超时
        import threading
        import time

        result = [None]
        exception = [None]

        def call_llm():
            try:
                result[0] = llm.call("1+1等于几？只回答数字。")
            except Exception as e:
                exception[0] = e

        # 启动调用线程
        thread = threading.Thread(target=call_llm)
        thread.daemon = True
        thread.start()

        # 等待最多10秒
        thread.join(timeout=10)

        if thread.is_alive():
            print("❌ CrewAI LLM调用超时 (10秒)")
            return False
        elif exception[0]:
            raise exception[0]
        else:
            print(f"✅ CrewAI LLM调用成功!")
            print(f"响应: {result[0]}")
            return True
        
    except Exception as e:
        print(f"❌ CrewAI LLM调用失败: {str(e)}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    print("CrewAI LLM测试开始")
    print("=" * 50)
    
    # 测试初始化
    llm = test_crewai_llm_init()
    
    # 测试调用
    if llm:
        call_ok = test_crewai_llm_call(llm)
    else:
        call_ok = False
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print(f"LLM初始化: {'✅ 成功' if llm else '❌ 失败'}")
    print(f"LLM调用: {'✅ 成功' if call_ok else '❌ 失败'}")
    
    if llm and call_ok:
        print("\n🎉 CrewAI LLM完全正常！")
    elif llm:
        print("\n⚠️ CrewAI LLM初始化成功但调用有问题。")
    else:
        print("\n❌ CrewAI LLM初始化失败。")
