2025-07-30 18:06:50 - numexpr.utils - INFO - Note: NumExpr detected 20 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 16.
2025-07-30 18:06:50 - numexpr.utils - INFO - NumExpr defaulting to 16 threads.
2025-07-30 18:06:53 - LawRAGEngine - INFO - 初始化法规检索引擎
2025-07-30 18:06:53 - LawRAGEngine - INFO - 法规RAG引擎使用设备: cpu
2025-07-30 18:06:53 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: D:\Users\Administrator\Desktop\3.01\sys3.0\models\paraphrase-multilingual-MiniLM-L12-v2
2025-07-30 18:06:54 - LawRAGEngine - INFO - 初始化重排序模型: D:\Users\Administrator\Desktop\3.01\sys3.0\models\bge-reranker-base
2025-07-30 18:06:54 - LawRAGEngine - INFO - 重排序模型初始化成功，使用设备: cpu
2025-07-30 18:06:54 - LawRAGEngine - INFO - 重排序模型预热完成
2025-07-30 18:06:54 - LawRAGEngine - INFO - 从 D:\Users\Administrator\Desktop\3.01\sys3.0\tools\law_rag_engine\vector 加载向量存储
2025-07-30 18:06:54 - LawRAGEngine - ERROR - 加载向量存储失败: Could not import faiss python package. Please install it with `pip install faiss-gpu` (for CUDA supported GPU) or `pip install faiss-cpu` (depending on Python version).
Traceback (most recent call last):
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 56, in dependable_faiss_import
    import faiss
ModuleNotFoundError: No module named 'faiss'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Users\Administrator\Desktop\3.01\sys3.0\tools\law_rag_engine\rag_engine.py", line 137, in __init__
    self.vectorstore = FAISS.load_local(
                       ~~~~~~~~~~~~~~~~^
        str(self.vector_store_path),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        self.embeddings,
        ^^^^^^^^^^^^^^^^
        allow_dangerous_deserialization=True  # 添加参数，允许反序列化
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 1204, in load_local
    faiss = dependable_faiss_import()
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 58, in dependable_faiss_import
    raise ImportError(
    ...<3 lines>...
    )
ImportError: Could not import faiss python package. Please install it with `pip install faiss-gpu` (for CUDA supported GPU) or `pip install faiss-cpu` (depending on Python version).
2025-07-30 18:06:54 - LawRAGEngine - INFO - 法规向量存储初始化完成
2025-07-30 18:06:55 - smart_graph_loader - INFO - 🚀 开始智能图谱数据检查和加载
2025-07-30 18:06:59 - smart_graph_loader - ERROR - ❌ 数据库连接失败: [4000] Neo4j服务不可用: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
2025-07-30 18:07:07 - opentelemetry.trace - WARNING - Overriding of current TracerProvider is not allowed
2025-07-30 18:07:08 - system - INFO - 系统信息: Python 3.13.5 | packaged by Anaconda, Inc. | (main, Jun 12 2025, 16:37:03) [MSC v.1929 64 bit (AMD64)], OS: Windows
2025-07-30 18:07:08 - system - INFO - CrewAI框架版本已加载，系统将使用CrewAI执行工作
2025-07-30 18:07:08 - system - INFO - 执行系统状态检查..
2025-07-30 18:07:08 - system - INFO - 
============================================================
2025-07-30 18:07:08 - system - INFO - = 系统运行中，将从网络收集数据
2025-07-30 18:07:08 - system - INFO - ============================================================

2025-07-30 18:07:08 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus, 超时: 60s, 重试: 3次
2025-07-30 18:07:12 - system - INFO - 系统状态检查结果:
2025-07-30 18:07:12 - system - INFO - - config: ok - 配置加载成功
2025-07-30 18:07:12 - system - INFO - - llm: ok - LLM配置有效，提供商: openai
2025-07-30 18:07:12 - system - INFO - - data_dirs: ok - 数据目录已准备就绪
2025-07-30 18:07:12 - system - ERROR - - neo4j: error - Neo4j连接失败: [4000] Neo4j服务不可用: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
2025-07-30 18:07:12 - system - WARNING - - gpu: warning - GPU不可用，使用CPU模式
2025-07-30 18:07:12 - system - ERROR - 
系统状态检查失败。以下组件存在问题:
- neo4j: Neo4j连接失败: [4000] Neo4j服务不可用: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
- gpu: GPU不可用，使用CPU模式
2025-07-30 18:07:24 - numexpr.utils - INFO - Note: NumExpr detected 20 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 16.
2025-07-30 18:07:24 - numexpr.utils - INFO - NumExpr defaulting to 16 threads.
2025-07-30 18:07:27 - LawRAGEngine - INFO - 初始化法规检索引擎
2025-07-30 18:07:27 - LawRAGEngine - INFO - 法规RAG引擎使用设备: cpu
2025-07-30 18:07:27 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: D:\Users\Administrator\Desktop\3.01\sys3.0\models\paraphrase-multilingual-MiniLM-L12-v2
2025-07-30 18:07:28 - LawRAGEngine - INFO - 初始化重排序模型: D:\Users\Administrator\Desktop\3.01\sys3.0\models\bge-reranker-base
2025-07-30 18:07:28 - LawRAGEngine - INFO - 重排序模型初始化成功，使用设备: cpu
2025-07-30 18:07:28 - LawRAGEngine - INFO - 重排序模型预热完成
2025-07-30 18:07:28 - LawRAGEngine - INFO - 从 D:\Users\Administrator\Desktop\3.01\sys3.0\tools\law_rag_engine\vector 加载向量存储
2025-07-30 18:07:28 - LawRAGEngine - ERROR - 加载向量存储失败: Could not import faiss python package. Please install it with `pip install faiss-gpu` (for CUDA supported GPU) or `pip install faiss-cpu` (depending on Python version).
Traceback (most recent call last):
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 56, in dependable_faiss_import
    import faiss
ModuleNotFoundError: No module named 'faiss'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Users\Administrator\Desktop\3.01\sys3.0\tools\law_rag_engine\rag_engine.py", line 137, in __init__
    self.vectorstore = FAISS.load_local(
                       ~~~~~~~~~~~~~~~~^
        str(self.vector_store_path),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        self.embeddings,
        ^^^^^^^^^^^^^^^^
        allow_dangerous_deserialization=True  # 添加参数，允许反序列化
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 1204, in load_local
    faiss = dependable_faiss_import()
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 58, in dependable_faiss_import
    raise ImportError(
    ...<3 lines>...
    )
ImportError: Could not import faiss python package. Please install it with `pip install faiss-gpu` (for CUDA supported GPU) or `pip install faiss-cpu` (depending on Python version).
2025-07-30 18:07:28 - LawRAGEngine - INFO - 法规向量存储初始化完成
2025-07-30 18:07:28 - smart_graph_loader - INFO - 🚀 开始智能图谱数据检查和加载
2025-07-30 18:07:32 - smart_graph_loader - ERROR - ❌ 数据库连接失败: [4000] Neo4j服务不可用: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
2025-07-30 18:07:36 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-07-30 18:07:37 - opentelemetry.trace - WARNING - Overriding of current TracerProvider is not allowed
2025-07-30 18:07:38 - system - ERROR - 
错误: 运行模式 'extract' 依赖于collect的输出，请提供输入文件
2025-07-30 18:15:58 - numexpr.utils - INFO - Note: NumExpr detected 20 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 16.
2025-07-30 18:15:58 - numexpr.utils - INFO - NumExpr defaulting to 16 threads.
2025-07-30 18:16:00 - LawRAGEngine - INFO - 初始化法规检索引擎
2025-07-30 18:16:00 - LawRAGEngine - INFO - 法规RAG引擎使用设备: cpu
2025-07-30 18:16:00 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: D:\Users\Administrator\Desktop\3.01\sys3.0\models\paraphrase-multilingual-MiniLM-L12-v2
2025-07-30 18:16:01 - LawRAGEngine - INFO - 初始化重排序模型: D:\Users\Administrator\Desktop\3.01\sys3.0\models\bge-reranker-base
2025-07-30 18:16:02 - LawRAGEngine - INFO - 重排序模型初始化成功，使用设备: cpu
2025-07-30 18:16:02 - LawRAGEngine - INFO - 重排序模型预热完成
2025-07-30 18:16:02 - LawRAGEngine - INFO - 从 D:\Users\Administrator\Desktop\3.01\sys3.0\tools\law_rag_engine\vector 加载向量存储
2025-07-30 18:16:02 - LawRAGEngine - ERROR - 加载向量存储失败: Could not import faiss python package. Please install it with `pip install faiss-gpu` (for CUDA supported GPU) or `pip install faiss-cpu` (depending on Python version).
Traceback (most recent call last):
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 56, in dependable_faiss_import
    import faiss
ModuleNotFoundError: No module named 'faiss'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Users\Administrator\Desktop\3.01\sys3.0\tools\law_rag_engine\rag_engine.py", line 137, in __init__
    self.vectorstore = FAISS.load_local(
                       ~~~~~~~~~~~~~~~~^
        str(self.vector_store_path),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        self.embeddings,
        ^^^^^^^^^^^^^^^^
        allow_dangerous_deserialization=True  # 添加参数，允许反序列化
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 1204, in load_local
    faiss = dependable_faiss_import()
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 58, in dependable_faiss_import
    raise ImportError(
    ...<3 lines>...
    )
ImportError: Could not import faiss python package. Please install it with `pip install faiss-gpu` (for CUDA supported GPU) or `pip install faiss-cpu` (depending on Python version).
2025-07-30 18:16:02 - LawRAGEngine - INFO - 法规向量存储初始化完成
2025-07-30 18:16:02 - smart_graph_loader - INFO - 🚀 开始智能图谱数据检查和加载
2025-07-30 18:16:06 - smart_graph_loader - ERROR - ❌ 数据库连接失败: [4000] Neo4j服务不可用: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
2025-07-30 18:16:09 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-07-30 18:16:10 - opentelemetry.trace - WARNING - Overriding of current TracerProvider is not allowed
2025-07-30 18:16:22 - numexpr.utils - INFO - Note: NumExpr detected 20 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 16.
2025-07-30 18:16:22 - numexpr.utils - INFO - NumExpr defaulting to 16 threads.
2025-07-30 18:16:25 - LawRAGEngine - INFO - 初始化法规检索引擎
2025-07-30 18:16:25 - LawRAGEngine - INFO - 法规RAG引擎使用设备: cpu
2025-07-30 18:16:25 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: D:\Users\Administrator\Desktop\3.01\sys3.0\models\paraphrase-multilingual-MiniLM-L12-v2
2025-07-30 18:16:26 - LawRAGEngine - INFO - 初始化重排序模型: D:\Users\Administrator\Desktop\3.01\sys3.0\models\bge-reranker-base
2025-07-30 18:16:26 - LawRAGEngine - INFO - 重排序模型初始化成功，使用设备: cpu
2025-07-30 18:16:26 - LawRAGEngine - INFO - 重排序模型预热完成
2025-07-30 18:16:26 - LawRAGEngine - INFO - 从 D:\Users\Administrator\Desktop\3.01\sys3.0\tools\law_rag_engine\vector 加载向量存储
2025-07-30 18:16:26 - LawRAGEngine - ERROR - 加载向量存储失败: Could not import faiss python package. Please install it with `pip install faiss-gpu` (for CUDA supported GPU) or `pip install faiss-cpu` (depending on Python version).
Traceback (most recent call last):
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 56, in dependable_faiss_import
    import faiss
ModuleNotFoundError: No module named 'faiss'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Users\Administrator\Desktop\3.01\sys3.0\tools\law_rag_engine\rag_engine.py", line 137, in __init__
    self.vectorstore = FAISS.load_local(
                       ~~~~~~~~~~~~~~~~^
        str(self.vector_store_path),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        self.embeddings,
        ^^^^^^^^^^^^^^^^
        allow_dangerous_deserialization=True  # 添加参数，允许反序列化
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 1204, in load_local
    faiss = dependable_faiss_import()
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 58, in dependable_faiss_import
    raise ImportError(
    ...<3 lines>...
    )
ImportError: Could not import faiss python package. Please install it with `pip install faiss-gpu` (for CUDA supported GPU) or `pip install faiss-cpu` (depending on Python version).
2025-07-30 18:16:26 - LawRAGEngine - INFO - 法规向量存储初始化完成
2025-07-30 18:16:27 - smart_graph_loader - INFO - 🚀 开始智能图谱数据检查和加载
2025-07-30 18:16:31 - smart_graph_loader - ERROR - ❌ 数据库连接失败: [4000] Neo4j服务不可用: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
2025-07-30 18:16:34 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-07-30 18:16:35 - opentelemetry.trace - WARNING - Overriding of current TracerProvider is not allowed
2025-07-30 18:16:36 - system - ERROR - 
错误: 运行模式 'extract' 依赖于collect的输出，请提供输入文件
2025-07-30 18:17:38 - numexpr.utils - INFO - Note: NumExpr detected 20 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 16.
2025-07-30 18:17:38 - numexpr.utils - INFO - NumExpr defaulting to 16 threads.
2025-07-30 18:17:40 - LawRAGEngine - INFO - 初始化法规检索引擎
2025-07-30 18:17:40 - LawRAGEngine - INFO - 法规RAG引擎使用设备: cpu
2025-07-30 18:17:40 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: D:\Users\Administrator\Desktop\3.01\sys3.0\models\paraphrase-multilingual-MiniLM-L12-v2
2025-07-30 18:17:41 - LawRAGEngine - INFO - 初始化重排序模型: D:\Users\Administrator\Desktop\3.01\sys3.0\models\bge-reranker-base
2025-07-30 18:17:42 - LawRAGEngine - INFO - 重排序模型初始化成功，使用设备: cpu
2025-07-30 18:17:42 - LawRAGEngine - INFO - 重排序模型预热完成
2025-07-30 18:17:42 - LawRAGEngine - INFO - 从 D:\Users\Administrator\Desktop\3.01\sys3.0\tools\law_rag_engine\vector 加载向量存储
2025-07-30 18:17:42 - LawRAGEngine - ERROR - 加载向量存储失败: Could not import faiss python package. Please install it with `pip install faiss-gpu` (for CUDA supported GPU) or `pip install faiss-cpu` (depending on Python version).
Traceback (most recent call last):
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 56, in dependable_faiss_import
    import faiss
ModuleNotFoundError: No module named 'faiss'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Users\Administrator\Desktop\3.01\sys3.0\tools\law_rag_engine\rag_engine.py", line 137, in __init__
    self.vectorstore = FAISS.load_local(
                       ~~~~~~~~~~~~~~~~^
        str(self.vector_store_path),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        self.embeddings,
        ^^^^^^^^^^^^^^^^
        allow_dangerous_deserialization=True  # 添加参数，允许反序列化
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 1204, in load_local
    faiss = dependable_faiss_import()
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 58, in dependable_faiss_import
    raise ImportError(
    ...<3 lines>...
    )
ImportError: Could not import faiss python package. Please install it with `pip install faiss-gpu` (for CUDA supported GPU) or `pip install faiss-cpu` (depending on Python version).
2025-07-30 18:17:42 - LawRAGEngine - INFO - 法规向量存储初始化完成
2025-07-30 18:17:42 - smart_graph_loader - INFO - 🚀 开始智能图谱数据检查和加载
2025-07-30 18:17:46 - smart_graph_loader - ERROR - ❌ 数据库连接失败: [4000] Neo4j服务不可用: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
2025-07-30 18:17:50 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-07-30 18:17:51 - opentelemetry.trace - WARNING - Overriding of current TracerProvider is not allowed
2025-07-30 18:17:52 - system - INFO - 系统信息: Python 3.13.5 | packaged by Anaconda, Inc. | (main, Jun 12 2025, 16:37:03) [MSC v.1929 64 bit (AMD64)], OS: Windows
2025-07-30 18:17:52 - system - INFO - CrewAI框架版本已加载，系统将使用CrewAI执行工作
2025-07-30 18:17:52 - system - INFO - 初始化控制器智能体..
2025-07-30 18:17:52 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus, 超时: 60s, 重试: 3次
2025-07-30 18:17:52 - ControllerAgent - INFO - 智能体 控制者 初始化完成
2025-07-30 18:17:52 - system - INFO - 开始执行任务，任务ID: TASK-20250730-01, 模式: extract
2025-07-30 18:17:52 - ControllerAgent - INFO - 开始执行任务，模式: extract, 任务ID: TASK-20250730-01
2025-07-30 18:17:52 - ControllerAgent - INFO - 初始化智能体
2025-07-30 18:17:52 - ControllerAgent - INFO - 初始化收集器智能体...
2025-07-30 18:17:52 - weibo_api - INFO - 微博搜索API初始化完成
2025-07-30 18:17:52 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus, 超时: 60s, 重试: 3次
2025-07-30 18:17:52 - CollectorAgent - INFO - 智能体 数据收集者 初始化完成
2025-07-30 18:17:52 - ControllerAgent - INFO - 初始化提取器智能体...
2025-07-30 18:17:52 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus, 超时: 60s, 重试: 3次
2025-07-30 18:17:52 - ExtractorAgent - INFO - 智能体 信息提取者 初始化完成
2025-07-30 18:17:52 - ControllerAgent - INFO - 初始化评估器智能体...
2025-07-30 18:17:52 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus, 超时: 60s, 重试: 3次
2025-07-30 18:17:52 - EvaluatorAgent - INFO - 智能体 风险评估者 初始化完成
2025-07-30 18:17:52 - ControllerAgent - INFO - 所有智能体初始化完成
2025-07-30 18:17:52 - ControllerAgent - INFO - 执行步骤: extract
2025-07-30 18:17:52 - ControllerAgent - INFO - 开始执行信息抽取工作流
2025-07-30 18:17:52 - ExtractorAgent - INFO - 开始执行信息提取任务
2025-07-30 18:17:52 - ExtractorAgent - INFO - 从文件加载微博数据: data/raw/test_sample.json
2025-07-30 18:17:52 - ExtractorAgent - INFO - 从文件加载了 10 条微博数据
2025-07-30 18:17:52 - ExtractorAgent - INFO - [信息抽取] |□□□□□□□□□□□□□□□□□□□□| 0% (0/10)
2025-07-30 18:17:52 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 39
2025-07-30 18:17:52 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 18:17:54 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 401 Unauthorized"
2025-07-30 18:17:54 - root - ERROR - LiteLLM call failed: litellm.AuthenticationError: AuthenticationError: OpenAIException - Incorrect API key provided: sk-1c627***********************c9cd. You can find your API key at https://platform.openai.com/account/api-keys.
2025-07-30 18:17:54 - ExtractorAgent - WARNING - LLM调用失败: litellm.AuthenticationError: AuthenticationError: OpenAIException - Incorrect API key provided: sk-1c627***********************c9cd. You can find your API key at https://platform.openai.com/account/api-keys.
2025-07-30 18:17:54 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'reason': 'LLM分析失败', 'confidence': 0.3}
2025-07-30 18:17:54 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - LLM分析失败
2025-07-30 18:17:54 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: LLM分析失败
2025-07-30 18:17:54 - ExtractorAgent - INFO - [信息抽取] |■■□□□□□□□□□□□□□□□□□□| 10% (1/10)
2025-07-30 18:17:54 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 950
2025-07-30 18:17:54 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 18:17:54 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 401 Unauthorized"
2025-07-30 18:17:54 - root - ERROR - LiteLLM call failed: litellm.AuthenticationError: AuthenticationError: OpenAIException - Incorrect API key provided: sk-1c627***********************c9cd. You can find your API key at https://platform.openai.com/account/api-keys.
2025-07-30 18:17:54 - ExtractorAgent - WARNING - LLM调用失败: litellm.AuthenticationError: AuthenticationError: OpenAIException - Incorrect API key provided: sk-1c627***********************c9cd. You can find your API key at https://platform.openai.com/account/api-keys.
2025-07-30 18:17:54 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'reason': 'LLM分析失败', 'confidence': 0.3}
2025-07-30 18:17:54 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - LLM分析失败
2025-07-30 18:17:54 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: LLM分析失败
2025-07-30 18:17:54 - ExtractorAgent - INFO - [信息抽取] |■■■■□□□□□□□□□□□□□□□□| 20% (2/10)
2025-07-30 18:17:54 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 492
2025-07-30 18:17:54 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 18:17:55 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 401 Unauthorized"
2025-07-30 18:17:55 - root - ERROR - LiteLLM call failed: litellm.AuthenticationError: AuthenticationError: OpenAIException - Incorrect API key provided: sk-1c627***********************c9cd. You can find your API key at https://platform.openai.com/account/api-keys.
2025-07-30 18:17:55 - ExtractorAgent - WARNING - LLM调用失败: litellm.AuthenticationError: AuthenticationError: OpenAIException - Incorrect API key provided: sk-1c627***********************c9cd. You can find your API key at https://platform.openai.com/account/api-keys.
2025-07-30 18:17:55 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'reason': 'LLM分析失败', 'confidence': 0.3}
2025-07-30 18:17:55 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - LLM分析失败
2025-07-30 18:17:55 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: LLM分析失败
2025-07-30 18:17:55 - ExtractorAgent - INFO - [信息抽取] |■■■■■■□□□□□□□□□□□□□□| 30% (3/10)
2025-07-30 18:17:55 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 1119
2025-07-30 18:17:55 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 18:17:55 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 401 Unauthorized"
2025-07-30 18:17:55 - root - ERROR - LiteLLM call failed: litellm.AuthenticationError: AuthenticationError: OpenAIException - Incorrect API key provided: sk-1c627***********************c9cd. You can find your API key at https://platform.openai.com/account/api-keys.
2025-07-30 18:17:55 - ExtractorAgent - WARNING - LLM调用失败: litellm.AuthenticationError: AuthenticationError: OpenAIException - Incorrect API key provided: sk-1c627***********************c9cd. You can find your API key at https://platform.openai.com/account/api-keys.
2025-07-30 18:17:55 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'reason': 'LLM分析失败', 'confidence': 0.3}
2025-07-30 18:17:55 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - LLM分析失败
2025-07-30 18:17:55 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: LLM分析失败
2025-07-30 18:17:55 - ExtractorAgent - INFO - [信息抽取] |■■■■■■■■□□□□□□□□□□□□| 40% (4/10)
2025-07-30 18:17:55 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 192
2025-07-30 18:17:55 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 18:17:55 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 401 Unauthorized"
2025-07-30 18:17:55 - root - ERROR - LiteLLM call failed: litellm.AuthenticationError: AuthenticationError: OpenAIException - Incorrect API key provided: sk-1c627***********************c9cd. You can find your API key at https://platform.openai.com/account/api-keys.
2025-07-30 18:17:55 - ExtractorAgent - WARNING - LLM调用失败: litellm.AuthenticationError: AuthenticationError: OpenAIException - Incorrect API key provided: sk-1c627***********************c9cd. You can find your API key at https://platform.openai.com/account/api-keys.
2025-07-30 18:17:55 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'reason': 'LLM分析失败', 'confidence': 0.3}
2025-07-30 18:17:55 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - LLM分析失败
2025-07-30 18:17:55 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: LLM分析失败
2025-07-30 18:17:55 - ExtractorAgent - INFO - [信息抽取] |■■■■■■■■■■□□□□□□□□□□| 50% (5/10)
2025-07-30 18:17:55 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 348
2025-07-30 18:17:55 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 18:17:56 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 401 Unauthorized"
2025-07-30 18:17:56 - root - ERROR - LiteLLM call failed: litellm.AuthenticationError: AuthenticationError: OpenAIException - Incorrect API key provided: sk-1c627***********************c9cd. You can find your API key at https://platform.openai.com/account/api-keys.
2025-07-30 18:17:56 - ExtractorAgent - WARNING - LLM调用失败: litellm.AuthenticationError: AuthenticationError: OpenAIException - Incorrect API key provided: sk-1c627***********************c9cd. You can find your API key at https://platform.openai.com/account/api-keys.
2025-07-30 18:17:56 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'reason': 'LLM分析失败', 'confidence': 0.3}
2025-07-30 18:17:56 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - LLM分析失败
2025-07-30 18:17:56 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: LLM分析失败
2025-07-30 18:17:56 - ExtractorAgent - INFO - [信息抽取] |■■■■■■■■■■■■□□□□□□□□| 60% (6/10)
2025-07-30 18:17:56 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 220
2025-07-30 18:17:56 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 18:17:56 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 401 Unauthorized"
2025-07-30 18:17:56 - root - ERROR - LiteLLM call failed: litellm.AuthenticationError: AuthenticationError: OpenAIException - Incorrect API key provided: sk-1c627***********************c9cd. You can find your API key at https://platform.openai.com/account/api-keys.
2025-07-30 18:17:56 - ExtractorAgent - WARNING - LLM调用失败: litellm.AuthenticationError: AuthenticationError: OpenAIException - Incorrect API key provided: sk-1c627***********************c9cd. You can find your API key at https://platform.openai.com/account/api-keys.
2025-07-30 18:17:56 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'reason': 'LLM分析失败', 'confidence': 0.3}
2025-07-30 18:17:56 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - LLM分析失败
2025-07-30 18:17:56 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: LLM分析失败
2025-07-30 18:17:56 - ExtractorAgent - INFO - [信息抽取] |■■■■■■■■■■■■■■□□□□□□| 70% (7/10)
2025-07-30 18:17:56 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 5211
2025-07-30 18:17:56 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 18:17:56 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 401 Unauthorized"
2025-07-30 18:17:56 - root - ERROR - LiteLLM call failed: litellm.AuthenticationError: AuthenticationError: OpenAIException - Incorrect API key provided: sk-1c627***********************c9cd. You can find your API key at https://platform.openai.com/account/api-keys.
2025-07-30 18:17:56 - ExtractorAgent - WARNING - LLM调用失败: litellm.AuthenticationError: AuthenticationError: OpenAIException - Incorrect API key provided: sk-1c627***********************c9cd. You can find your API key at https://platform.openai.com/account/api-keys.
2025-07-30 18:17:56 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'reason': 'LLM分析失败', 'confidence': 0.3}
2025-07-30 18:17:56 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - LLM分析失败
2025-07-30 18:17:56 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: LLM分析失败
2025-07-30 18:17:56 - ExtractorAgent - INFO - [信息抽取] |■■■■■■■■■■■■■■■■□□□□| 80% (8/10)
2025-07-30 18:17:56 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 58
2025-07-30 18:17:56 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 18:17:57 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 401 Unauthorized"
2025-07-30 18:17:57 - root - ERROR - LiteLLM call failed: litellm.AuthenticationError: AuthenticationError: OpenAIException - Incorrect API key provided: sk-1c627***********************c9cd. You can find your API key at https://platform.openai.com/account/api-keys.
2025-07-30 18:17:57 - ExtractorAgent - WARNING - LLM调用失败: litellm.AuthenticationError: AuthenticationError: OpenAIException - Incorrect API key provided: sk-1c627***********************c9cd. You can find your API key at https://platform.openai.com/account/api-keys.
2025-07-30 18:17:57 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'reason': 'LLM分析失败', 'confidence': 0.3}
2025-07-30 18:17:57 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - LLM分析失败
2025-07-30 18:17:57 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: LLM分析失败
2025-07-30 18:17:57 - ExtractorAgent - INFO - [信息抽取] |■■■■■■■■■■■■■■■■■■□□| 90% (9/10)
2025-07-30 18:17:57 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 540
2025-07-30 18:17:57 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 18:17:57 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 401 Unauthorized"
2025-07-30 18:17:57 - root - ERROR - LiteLLM call failed: litellm.AuthenticationError: AuthenticationError: OpenAIException - Incorrect API key provided: sk-1c627***********************c9cd. You can find your API key at https://platform.openai.com/account/api-keys.
2025-07-30 18:17:57 - ExtractorAgent - WARNING - LLM调用失败: litellm.AuthenticationError: AuthenticationError: OpenAIException - Incorrect API key provided: sk-1c627***********************c9cd. You can find your API key at https://platform.openai.com/account/api-keys.
2025-07-30 18:17:57 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'reason': 'LLM分析失败', 'confidence': 0.3}
2025-07-30 18:17:57 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - LLM分析失败
2025-07-30 18:17:57 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: LLM分析失败
2025-07-30 18:17:57 - ExtractorAgent - INFO - [信息抽取] |■■■■■■■■■■■■■■■■■■■■| 100% (10/10)
2025-07-30 18:17:57 - ExtractorAgent - INFO - [信息抽取] |■■■■■■■■■■■■■■■■■■■■| 100% (10/10)
2025-07-30 18:17:57 - ExtractorAgent - INFO - [信息抽取] 完成！共处理 10 项
2025-07-30 18:17:57 - ExtractorAgent - INFO - 
信息提取完成统计:
- 处理微博数: 10
- 成功提取: 0 条隐患
- 跳过抽取: 10 条 (语义预判断)
- 低质量过滤: 0 条
- 错误失败: 0 条
- 成功率: 0.0%
- 处理耗时: 5.01 秒
- 平均耗时: 0.50 秒/条
        
2025-07-30 18:17:57 - ExtractorAgent - INFO - 结构化数据已保存到: D:\Users\Administrator\Desktop\3.01\sys3.0\data\structured\TASK-20250730-01.json
2025-07-30 18:17:57 - DataService - INFO - 已保存任务 TASK-20250730-01 的 structured 阶段数据到 D:\Users\Administrator\Desktop\3.01\sys3.0\data\structured\TASK-20250730-01.json
2025-07-30 18:17:57 - ExtractorAgent - INFO - 结构化数据已保存到任务: TASK-20250730-01
2025-07-30 18:17:57 - ExtractorAgent - WARNING - 未提取到任何隐患信息，但已保存空结果
2025-07-30 18:17:57 - ControllerAgent - INFO - 任务执行完成，耗时: 5.03秒
2025-07-30 18:17:57 - system - INFO - 任务执行完成，耗时: 5.03 秒
2025-07-30 18:17:57 - system - INFO - 
============================================================
2025-07-30 18:17:57 - system - INFO - = 任务执行成功
2025-07-30 18:17:57 - system - INFO - = 任务ID: TASK-20250730-01
2025-07-30 18:17:57 - system - INFO - = 执行时间: 5.03 秒
2025-07-30 18:17:57 - system - INFO - ============================================================

2025-07-30 18:20:37 - numexpr.utils - INFO - Note: NumExpr detected 20 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 16.
2025-07-30 18:20:37 - numexpr.utils - INFO - NumExpr defaulting to 16 threads.
2025-07-30 18:20:39 - LawRAGEngine - INFO - 初始化法规检索引擎
2025-07-30 18:20:39 - LawRAGEngine - INFO - 法规RAG引擎使用设备: cpu
2025-07-30 18:20:39 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: D:\Users\Administrator\Desktop\3.01\sys3.0\models\paraphrase-multilingual-MiniLM-L12-v2
2025-07-30 18:20:41 - LawRAGEngine - INFO - 初始化重排序模型: D:\Users\Administrator\Desktop\3.01\sys3.0\models\bge-reranker-base
2025-07-30 18:20:41 - LawRAGEngine - INFO - 重排序模型初始化成功，使用设备: cpu
2025-07-30 18:20:41 - LawRAGEngine - INFO - 重排序模型预热完成
2025-07-30 18:20:41 - LawRAGEngine - INFO - 从 D:\Users\Administrator\Desktop\3.01\sys3.0\tools\law_rag_engine\vector 加载向量存储
2025-07-30 18:20:41 - LawRAGEngine - ERROR - 加载向量存储失败: Could not import faiss python package. Please install it with `pip install faiss-gpu` (for CUDA supported GPU) or `pip install faiss-cpu` (depending on Python version).
Traceback (most recent call last):
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 56, in dependable_faiss_import
    import faiss
ModuleNotFoundError: No module named 'faiss'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Users\Administrator\Desktop\3.01\sys3.0\tools\law_rag_engine\rag_engine.py", line 137, in __init__
    self.vectorstore = FAISS.load_local(
                       ~~~~~~~~~~~~~~~~^
        str(self.vector_store_path),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        self.embeddings,
        ^^^^^^^^^^^^^^^^
        allow_dangerous_deserialization=True  # 添加参数，允许反序列化
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 1204, in load_local
    faiss = dependable_faiss_import()
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 58, in dependable_faiss_import
    raise ImportError(
    ...<3 lines>...
    )
ImportError: Could not import faiss python package. Please install it with `pip install faiss-gpu` (for CUDA supported GPU) or `pip install faiss-cpu` (depending on Python version).
2025-07-30 18:20:41 - LawRAGEngine - INFO - 法规向量存储初始化完成
2025-07-30 18:20:41 - smart_graph_loader - INFO - 🚀 开始智能图谱数据检查和加载
2025-07-30 18:20:45 - smart_graph_loader - ERROR - ❌ 数据库连接失败: [4000] Neo4j服务不可用: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
2025-07-30 18:20:49 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-07-30 18:20:50 - opentelemetry.trace - WARNING - Overriding of current TracerProvider is not allowed
2025-07-30 18:20:51 - system - INFO - 系统信息: Python 3.13.5 | packaged by Anaconda, Inc. | (main, Jun 12 2025, 16:37:03) [MSC v.1929 64 bit (AMD64)], OS: Windows
2025-07-30 18:20:51 - system - INFO - CrewAI框架版本已加载，系统将使用CrewAI执行工作
2025-07-30 18:20:51 - system - INFO - 初始化控制器智能体..
2025-07-30 18:20:51 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus, 超时: 60s, 重试: 3次
2025-07-30 18:20:51 - ControllerAgent - INFO - 智能体 控制者 初始化完成
2025-07-30 18:20:51 - system - INFO - 开始执行任务，任务ID: TASK-20250730-02, 模式: extract
2025-07-30 18:20:51 - ControllerAgent - INFO - 开始执行任务，模式: extract, 任务ID: TASK-20250730-02
2025-07-30 18:20:51 - ControllerAgent - INFO - 初始化智能体
2025-07-30 18:20:51 - ControllerAgent - INFO - 初始化收集器智能体...
2025-07-30 18:20:51 - weibo_api - INFO - 微博搜索API初始化完成
2025-07-30 18:20:51 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus, 超时: 60s, 重试: 3次
2025-07-30 18:20:51 - CollectorAgent - INFO - 智能体 数据收集者 初始化完成
2025-07-30 18:20:51 - ControllerAgent - INFO - 初始化提取器智能体...
2025-07-30 18:20:51 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus, 超时: 60s, 重试: 3次
2025-07-30 18:20:51 - ExtractorAgent - INFO - 智能体 信息提取者 初始化完成
2025-07-30 18:20:51 - ControllerAgent - INFO - 初始化评估器智能体...
2025-07-30 18:20:51 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus, 超时: 60s, 重试: 3次
2025-07-30 18:20:51 - EvaluatorAgent - INFO - 智能体 风险评估者 初始化完成
2025-07-30 18:20:51 - ControllerAgent - INFO - 所有智能体初始化完成
2025-07-30 18:20:51 - ControllerAgent - INFO - 执行步骤: extract
2025-07-30 18:20:51 - ControllerAgent - INFO - 开始执行信息抽取工作流
2025-07-30 18:20:51 - ExtractorAgent - INFO - 开始执行信息提取任务
2025-07-30 18:20:51 - ExtractorAgent - INFO - 从文件加载微博数据: data/raw/test_sample.json
2025-07-30 18:20:51 - ExtractorAgent - INFO - 从文件加载了 10 条微博数据
2025-07-30 18:20:51 - ExtractorAgent - INFO - [信息抽取] |□□□□□□□□□□□□□□□□□□□□| 0% (0/10)
2025-07-30 18:20:51 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 39
2025-07-30 18:20:51 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 18:20:58 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-30 18:20:58 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-30 18:20:58 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:20:58 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:20:58 - LiteLLM - INFO - selected model name for cost calculation: qwen-plus
2025-07-30 18:20:58 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:20:58 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'domain_relevance': 1.0, 'hazard_existence': 0.0, 'current_status': 1.0, 'specificity': 0.0, 'actionability': 0.0, 'content_nature': 0.0, 'overall_confidence': 0.95, 'content_type': 'education', 'reason': '该微博内容发布了一篇关于《地基基础沉降处理方法有哪些？》的科普文章，属于建筑安全领域的知识普及内容。虽然涉及建筑安全领域（domain_relevance为1.0），但并未描述任何当前存在的具体安全隐患，也没有提供任何可定位的地点信息或实际问题，仅是对相关处理方法的介绍。根据判断原则，该内容属于教育培训/知识科普类内容，不属于需要处理的建筑安全隐患举报。'}
2025-07-30 18:20:58 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - 该微博内容发布了一篇关于《地基基础沉降处理方法有哪些？》的科普文章，属于建筑安全领域的知识普及内容。虽然涉及建筑安全领域（domain_relevance为1.0），但并未描述任何当前存在的具体安全隐患，也没有提供任何可定位的地点信息或实际问题，仅是对相关处理方法的介绍。根据判断原则，该内容属于教育培训/知识科普类内容，不属于需要处理的建筑安全隐患举报。
2025-07-30 18:20:58 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:20:58 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: 该微博内容发布了一篇关于《地基基础沉降处理方法有哪些？》的科普文章，属于建筑安全领域的知识普及内容。虽然涉及建筑安全领域（domain_relevance为1.0），但并未描述任何当前存在的具体安全隐患，也没有提供任何可定位的地点信息或实际问题，仅是对相关处理方法的介绍。根据判断原则，该内容属于教育培训/知识科普类内容，不属于需要处理的建筑安全隐患举报。
2025-07-30 18:20:58 - LiteLLM - INFO - selected model name for cost calculation: qwen-plus
2025-07-30 18:20:58 - ExtractorAgent - INFO - [信息抽取] |■■□□□□□□□□□□□□□□□□□□| 10% (1/10)
2025-07-30 18:20:58 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 950
2025-07-30 18:20:58 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 18:21:04 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-30 18:21:04 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-30 18:21:04 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:04 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:04 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:04 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'domain_relevance': 0.2, 'hazard_existence': 0.1, 'current_status': 0.1, 'specificity': 0.3, 'actionability': 0.1, 'content_nature': 0.1, 'overall_confidence': 0.15, 'content_type': 'education', 'reason': '该微博内容主要为关于比萨斜塔的历史背景、建筑演变、旅游建议及当地美食的科普性介绍，不属于建筑安全领域的隐患举报。虽然提到了比萨斜塔因地基问题而倾斜的历史事实，但这是历史事件，且已通过工程干预稳定，并非当前存在的建筑安全隐患。内容未涉及任何当前建筑结构安全、施工、消防、电气等可处理的实际问题，不具备可操作性。整体属于建筑知识普及与旅游分享，不符合建筑安全隐患举报的核心判断原则。'}
2025-07-30 18:21:04 - LiteLLM - INFO - selected model name for cost calculation: qwen-plus
2025-07-30 18:21:04 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - 该微博内容主要为关于比萨斜塔的历史背景、建筑演变、旅游建议及当地美食的科普性介绍，不属于建筑安全领域的隐患举报。虽然提到了比萨斜塔因地基问题而倾斜的历史事实，但这是历史事件，且已通过工程干预稳定，并非当前存在的建筑安全隐患。内容未涉及任何当前建筑结构安全、施工、消防、电气等可处理的实际问题，不具备可操作性。整体属于建筑知识普及与旅游分享，不符合建筑安全隐患举报的核心判断原则。
2025-07-30 18:21:04 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:04 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: 该微博内容主要为关于比萨斜塔的历史背景、建筑演变、旅游建议及当地美食的科普性介绍，不属于建筑安全领域的隐患举报。虽然提到了比萨斜塔因地基问题而倾斜的历史事实，但这是历史事件，且已通过工程干预稳定，并非当前存在的建筑安全隐患。内容未涉及任何当前建筑结构安全、施工、消防、电气等可处理的实际问题，不具备可操作性。整体属于建筑知识普及与旅游分享，不符合建筑安全隐患举报的核心判断原则。
2025-07-30 18:21:04 - ExtractorAgent - INFO - [信息抽取] |■■■■□□□□□□□□□□□□□□□□| 20% (2/10)
2025-07-30 18:21:04 - LiteLLM - INFO - selected model name for cost calculation: qwen-plus
2025-07-30 18:21:04 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 492
2025-07-30 18:21:04 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 18:21:09 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-30 18:21:09 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-30 18:21:09 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:09 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:09 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:09 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'domain_relevance': 0.9, 'hazard_existence': 0.3, 'current_status': 0.4, 'specificity': 0.7, 'actionability': 0.3, 'content_nature': 0.2, 'overall_confidence': 0.4, 'content_type': 'education', 'reason': '该微博内容从专业角度看，属于建筑安全领域（地基设计、结构安全），但其内容并非举报当前存在的安全隐患，而是围绕砖窑地基深度的设计建议进行技术性说明，具有明显的知识普及和工程指导性质。虽然提到了具体地点（山西省闻喜县礼元镇小郝村），具有一定的具体性，但并未指出某处建筑已经存在或正在发生的结构安全问题，也未提到是否已施工或存在不合规行为。因此，不属于需要处理的建筑安全隐患举报，而是一篇建筑安全技术建议类内容，归类为‘教育培训内容’或‘安全知识科普’。'}
2025-07-30 18:21:09 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - 该微博内容从专业角度看，属于建筑安全领域（地基设计、结构安全），但其内容并非举报当前存在的安全隐患，而是围绕砖窑地基深度的设计建议进行技术性说明，具有明显的知识普及和工程指导性质。虽然提到了具体地点（山西省闻喜县礼元镇小郝村），具有一定的具体性，但并未指出某处建筑已经存在或正在发生的结构安全问题，也未提到是否已施工或存在不合规行为。因此，不属于需要处理的建筑安全隐患举报，而是一篇建筑安全技术建议类内容，归类为‘教育培训内容’或‘安全知识科普’。
2025-07-30 18:21:09 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: 该微博内容从专业角度看，属于建筑安全领域（地基设计、结构安全），但其内容并非举报当前存在的安全隐患，而是围绕砖窑地基深度的设计建议进行技术性说明，具有明显的知识普及和工程指导性质。虽然提到了具体地点（山西省闻喜县礼元镇小郝村），具有一定的具体性，但并未指出某处建筑已经存在或正在发生的结构安全问题，也未提到是否已施工或存在不合规行为。因此，不属于需要处理的建筑安全隐患举报，而是一篇建筑安全技术建议类内容，归类为‘教育培训内容’或‘安全知识科普’。
2025-07-30 18:21:09 - LiteLLM - INFO - selected model name for cost calculation: qwen-plus
2025-07-30 18:21:09 - ExtractorAgent - INFO - [信息抽取] |■■■■■■□□□□□□□□□□□□□□| 30% (3/10)
2025-07-30 18:21:09 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 1119
2025-07-30 18:21:09 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:09 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 18:21:09 - LiteLLM - INFO - selected model name for cost calculation: qwen-plus
2025-07-30 18:21:14 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-30 18:21:14 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-30 18:21:14 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:14 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:14 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:14 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'domain_relevance': 0.3, 'hazard_existence': 0.1, 'current_status': 0.2, 'specificity': 0.2, 'actionability': 0.1, 'content_nature': 0.1, 'overall_confidence': 0.2, 'content_type': 'non_construction', 'reason': '该微博内容为关于雅鲁藏布江下游水电工程的投资分析与市场预测，主要涉及施工技术、设备需求、材料供应、企业投资机会等经济与工程规划内容。虽然提到了施工难度、地质挑战（如深覆盖层、地壳运动）等地质工程问题，但并未描述当前存在的具体建筑安全隐患，也未提供可定位的具体建筑项目或现场问题。内容偏向于行业研究和投资建议，而非建筑安全领域的隐患举报。因此，不符合建筑安全隐患举报的核心判断标准。'}
2025-07-30 18:21:14 - LiteLLM - INFO - selected model name for cost calculation: qwen-plus
2025-07-30 18:21:14 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - 该微博内容为关于雅鲁藏布江下游水电工程的投资分析与市场预测，主要涉及施工技术、设备需求、材料供应、企业投资机会等经济与工程规划内容。虽然提到了施工难度、地质挑战（如深覆盖层、地壳运动）等地质工程问题，但并未描述当前存在的具体建筑安全隐患，也未提供可定位的具体建筑项目或现场问题。内容偏向于行业研究和投资建议，而非建筑安全领域的隐患举报。因此，不符合建筑安全隐患举报的核心判断标准。
2025-07-30 18:21:14 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:14 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: 该微博内容为关于雅鲁藏布江下游水电工程的投资分析与市场预测，主要涉及施工技术、设备需求、材料供应、企业投资机会等经济与工程规划内容。虽然提到了施工难度、地质挑战（如深覆盖层、地壳运动）等地质工程问题，但并未描述当前存在的具体建筑安全隐患，也未提供可定位的具体建筑项目或现场问题。内容偏向于行业研究和投资建议，而非建筑安全领域的隐患举报。因此，不符合建筑安全隐患举报的核心判断标准。
2025-07-30 18:21:14 - LiteLLM - INFO - selected model name for cost calculation: qwen-plus
2025-07-30 18:21:14 - ExtractorAgent - INFO - [信息抽取] |■■■■■■■■□□□□□□□□□□□□| 40% (4/10)
2025-07-30 18:21:14 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 192
2025-07-30 18:21:14 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 18:21:20 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-30 18:21:20 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-30 18:21:20 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:20 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:20 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:20 - LiteLLM - INFO - selected model name for cost calculation: qwen-plus
2025-07-30 18:21:20 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'domain_relevance': 0.8, 'hazard_existence': 0.2, 'current_status': 0.2, 'specificity': 0.1, 'actionability': 0.1, 'content_nature': 0.1, 'overall_confidence': 0.9, 'content_type': 'education', 'reason': '该微博内容涉及围堰在建筑施工中可能面临的雨水冲刷和腐蚀问题，属于建筑安全领域（domain_relevance较高）。但其核心目的是介绍狄林704涂料的性能，如柔韧性、抗酸碱、耐溶剂等，属于产品宣传与技术科普内容，而非报告当前存在的具体安全隐患。微博中未提及具体地点、未指出某地围堰存在实际腐蚀或结构问题，也未表达需要整改或检查的需求（specificity和actionability低）。内容性质属于安全知识科普和产品推广，不属于需要处理的隐患举报（content_nature为教育类）。综合判断，不应提取为建筑安全隐患举报。'}
2025-07-30 18:21:20 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:20 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - 该微博内容涉及围堰在建筑施工中可能面临的雨水冲刷和腐蚀问题，属于建筑安全领域（domain_relevance较高）。但其核心目的是介绍狄林704涂料的性能，如柔韧性、抗酸碱、耐溶剂等，属于产品宣传与技术科普内容，而非报告当前存在的具体安全隐患。微博中未提及具体地点、未指出某地围堰存在实际腐蚀或结构问题，也未表达需要整改或检查的需求（specificity和actionability低）。内容性质属于安全知识科普和产品推广，不属于需要处理的隐患举报（content_nature为教育类）。综合判断，不应提取为建筑安全隐患举报。
2025-07-30 18:21:20 - LiteLLM - INFO - selected model name for cost calculation: qwen-plus
2025-07-30 18:21:20 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: 该微博内容涉及围堰在建筑施工中可能面临的雨水冲刷和腐蚀问题，属于建筑安全领域（domain_relevance较高）。但其核心目的是介绍狄林704涂料的性能，如柔韧性、抗酸碱、耐溶剂等，属于产品宣传与技术科普内容，而非报告当前存在的具体安全隐患。微博中未提及具体地点、未指出某地围堰存在实际腐蚀或结构问题，也未表达需要整改或检查的需求（specificity和actionability低）。内容性质属于安全知识科普和产品推广，不属于需要处理的隐患举报（content_nature为教育类）。综合判断，不应提取为建筑安全隐患举报。
2025-07-30 18:21:20 - ExtractorAgent - INFO - [信息抽取] |■■■■■■■■■■□□□□□□□□□□| 50% (5/10)
2025-07-30 18:21:20 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 348
2025-07-30 18:21:20 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 18:21:26 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-30 18:21:26 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-30 18:21:26 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:26 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:26 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:26 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'domain_relevance': 0.8, 'hazard_existence': 0.6, 'current_status': 0.7, 'specificity': 0.7, 'actionability': 0.4, 'content_nature': 0.3, 'overall_confidence': 0.6, 'content_type': 'non_construction', 'reason': '该微博内容涉及的是日本关西机场的长期地基沉降问题，属于建筑结构安全范畴，因此在领域相关性上具有一定关联（domain_relevance: 0.8）。但该问题属于长期工程挑战，并非突发或紧急的安全隐患，且已有工程措施进行延缓，说明问题处于可控状态（hazard_existence: 0.6，current_status: 0.7）。微博内容提到了具体地点（关西机场第一、第二人工岛），具有一定的具体性（specificity: 0.7），但由于是国外项目，国内无法采取实地整改措施（actionability: 0.4）。此外，内容本质是对已有报道的纠错和信息澄清，而非对当前国内建筑安全问题的举报（content_nature: 0.3）。综上，该内容不属于需要处理的建筑安全隐患举报。'}
2025-07-30 18:21:26 - LiteLLM - INFO - selected model name for cost calculation: qwen-plus
2025-07-30 18:21:26 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - 该微博内容涉及的是日本关西机场的长期地基沉降问题，属于建筑结构安全范畴，因此在领域相关性上具有一定关联（domain_relevance: 0.8）。但该问题属于长期工程挑战，并非突发或紧急的安全隐患，且已有工程措施进行延缓，说明问题处于可控状态（hazard_existence: 0.6，current_status: 0.7）。微博内容提到了具体地点（关西机场第一、第二人工岛），具有一定的具体性（specificity: 0.7），但由于是国外项目，国内无法采取实地整改措施（actionability: 0.4）。此外，内容本质是对已有报道的纠错和信息澄清，而非对当前国内建筑安全问题的举报（content_nature: 0.3）。综上，该内容不属于需要处理的建筑安全隐患举报。
2025-07-30 18:21:26 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:26 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: 该微博内容涉及的是日本关西机场的长期地基沉降问题，属于建筑结构安全范畴，因此在领域相关性上具有一定关联（domain_relevance: 0.8）。但该问题属于长期工程挑战，并非突发或紧急的安全隐患，且已有工程措施进行延缓，说明问题处于可控状态（hazard_existence: 0.6，current_status: 0.7）。微博内容提到了具体地点（关西机场第一、第二人工岛），具有一定的具体性（specificity: 0.7），但由于是国外项目，国内无法采取实地整改措施（actionability: 0.4）。此外，内容本质是对已有报道的纠错和信息澄清，而非对当前国内建筑安全问题的举报（content_nature: 0.3）。综上，该内容不属于需要处理的建筑安全隐患举报。
2025-07-30 18:21:26 - LiteLLM - INFO - selected model name for cost calculation: qwen-plus
2025-07-30 18:21:26 - ExtractorAgent - INFO - [信息抽取] |■■■■■■■■■■■■□□□□□□□□| 60% (6/10)
2025-07-30 18:21:26 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 220
2025-07-30 18:21:26 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 18:21:31 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-30 18:21:31 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-30 18:21:31 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:31 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:31 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:31 - LiteLLM - INFO - selected model name for cost calculation: qwen-plus
2025-07-30 18:21:31 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'domain_relevance': 0.2, 'hazard_existence': 0.4, 'current_status': 1.0, 'specificity': 0.3, 'actionability': 0.2, 'content_nature': 0.3, 'overall_confidence': 0.35, 'content_type': 'non_construction', 'reason': '该微博内容主要围绕杭州自来水出现异味的问题展开分析，虽然提到了可能的建筑相关因素如‘地基沉降’、‘清水池污染’等术语，但整体讨论聚焦于城市供水系统安全，属于市政基础设施或环境安全范畴，而非传统意义上的建筑安全（如结构、施工、消防、电气等）。内容虽为当前问题（current_status=1），但缺乏具体建筑位置、具体建筑结构或设施的安全隐患描述，不具备可操作性（actionability低）。作者也明确表示‘静待调查结果’，表明其内容为推测分析而非正式举报。因此，该内容不属于需要提取的建筑安全隐患举报。'}
2025-07-30 18:21:31 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:31 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - 该微博内容主要围绕杭州自来水出现异味的问题展开分析，虽然提到了可能的建筑相关因素如‘地基沉降’、‘清水池污染’等术语，但整体讨论聚焦于城市供水系统安全，属于市政基础设施或环境安全范畴，而非传统意义上的建筑安全（如结构、施工、消防、电气等）。内容虽为当前问题（current_status=1），但缺乏具体建筑位置、具体建筑结构或设施的安全隐患描述，不具备可操作性（actionability低）。作者也明确表示‘静待调查结果’，表明其内容为推测分析而非正式举报。因此，该内容不属于需要提取的建筑安全隐患举报。
2025-07-30 18:21:31 - LiteLLM - INFO - selected model name for cost calculation: qwen-plus
2025-07-30 18:21:31 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: 该微博内容主要围绕杭州自来水出现异味的问题展开分析，虽然提到了可能的建筑相关因素如‘地基沉降’、‘清水池污染’等术语，但整体讨论聚焦于城市供水系统安全，属于市政基础设施或环境安全范畴，而非传统意义上的建筑安全（如结构、施工、消防、电气等）。内容虽为当前问题（current_status=1），但缺乏具体建筑位置、具体建筑结构或设施的安全隐患描述，不具备可操作性（actionability低）。作者也明确表示‘静待调查结果’，表明其内容为推测分析而非正式举报。因此，该内容不属于需要提取的建筑安全隐患举报。
2025-07-30 18:21:31 - ExtractorAgent - INFO - [信息抽取] |■■■■■■■■■■■■■■□□□□□□| 70% (7/10)
2025-07-30 18:21:31 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 5211
2025-07-30 18:21:31 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 18:21:40 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-30 18:21:40 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-30 18:21:40 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:40 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:40 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:40 - LiteLLM - INFO - selected model name for cost calculation: qwen-plus
2025-07-30 18:21:40 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'domain_relevance': 0.9, 'hazard_existence': 0.1, 'current_status': 0.1, 'specificity': 0.3, 'actionability': 0.2, 'content_nature': 0.1, 'overall_confidence': 0.85, 'content_type': 'education', 'reason': '该微博内容属于建筑安全领域（domain_relevance=0.9），详细介绍了水中不分散流态水泥的配方及其在注浆、防水堵漏、岩体加固等施工中的应用技术，具有较强的专业性。但内容本质是技术培训与施工方法介绍（content_nature=0.1），并非描述当前存在的具体建筑安全隐患。虽然涉及注浆施工、地下室防水、管廊治理等安全相关主题，但未指出任何具体项目、地点或实际存在的问题（specificity=0.3），也无明确的隐患描述或举报意图（hazard_existence=0.1）。内容偏向知识普及和操作指南，不具备可操作性（actionability=0.2），且未说明问题是否正在发生或需要立即处理（current_status=0.1）。综上，不属于需要处理的建筑安全隐患举报。'}
2025-07-30 18:21:40 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:40 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - 该微博内容属于建筑安全领域（domain_relevance=0.9），详细介绍了水中不分散流态水泥的配方及其在注浆、防水堵漏、岩体加固等施工中的应用技术，具有较强的专业性。但内容本质是技术培训与施工方法介绍（content_nature=0.1），并非描述当前存在的具体建筑安全隐患。虽然涉及注浆施工、地下室防水、管廊治理等安全相关主题，但未指出任何具体项目、地点或实际存在的问题（specificity=0.3），也无明确的隐患描述或举报意图（hazard_existence=0.1）。内容偏向知识普及和操作指南，不具备可操作性（actionability=0.2），且未说明问题是否正在发生或需要立即处理（current_status=0.1）。综上，不属于需要处理的建筑安全隐患举报。
2025-07-30 18:21:40 - LiteLLM - INFO - selected model name for cost calculation: qwen-plus
2025-07-30 18:21:40 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: 该微博内容属于建筑安全领域（domain_relevance=0.9），详细介绍了水中不分散流态水泥的配方及其在注浆、防水堵漏、岩体加固等施工中的应用技术，具有较强的专业性。但内容本质是技术培训与施工方法介绍（content_nature=0.1），并非描述当前存在的具体建筑安全隐患。虽然涉及注浆施工、地下室防水、管廊治理等安全相关主题，但未指出任何具体项目、地点或实际存在的问题（specificity=0.3），也无明确的隐患描述或举报意图（hazard_existence=0.1）。内容偏向知识普及和操作指南，不具备可操作性（actionability=0.2），且未说明问题是否正在发生或需要立即处理（current_status=0.1）。综上，不属于需要处理的建筑安全隐患举报。
2025-07-30 18:21:40 - ExtractorAgent - INFO - [信息抽取] |■■■■■■■■■■■■■■■■□□□□| 80% (8/10)
2025-07-30 18:21:40 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 58
2025-07-30 18:21:40 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 18:21:47 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-30 18:21:47 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-30 18:21:47 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:47 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:47 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:47 - LiteLLM - INFO - selected model name for cost calculation: qwen-plus
2025-07-30 18:21:47 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'domain_relevance': 0.1, 'hazard_existence': 0.1, 'current_status': 0.5, 'specificity': 0.2, 'actionability': 0.1, 'content_nature': 0.1, 'overall_confidence': 0.9, 'content_type': 'non_construction', 'reason': '该微博内容主要介绍的是在沙漠铁路沿线使用草方格与高立式沙障进行生态防护和绿化带建设的技术，属于环境保护与生态治理范畴，而非建筑安全领域。内容未涉及建筑结构、施工安全、消防安全、电气安全等建筑相关安全问题，也未指出任何具体的安全隐患或需要整改的问题。虽然提到了工程措施，但其目的是防风固沙、保护铁路生态环境，不具备建筑安全隐患举报的特征。此外，内容更偏向科普性质，未提供可定位的具体地点，也无明确的当前安全隐患存在描述，不具备实地检查或整改的可操作性。'}
2025-07-30 18:21:47 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:47 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - 该微博内容主要介绍的是在沙漠铁路沿线使用草方格与高立式沙障进行生态防护和绿化带建设的技术，属于环境保护与生态治理范畴，而非建筑安全领域。内容未涉及建筑结构、施工安全、消防安全、电气安全等建筑相关安全问题，也未指出任何具体的安全隐患或需要整改的问题。虽然提到了工程措施，但其目的是防风固沙、保护铁路生态环境，不具备建筑安全隐患举报的特征。此外，内容更偏向科普性质，未提供可定位的具体地点，也无明确的当前安全隐患存在描述，不具备实地检查或整改的可操作性。
2025-07-30 18:21:47 - LiteLLM - INFO - selected model name for cost calculation: qwen-plus
2025-07-30 18:21:47 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: 该微博内容主要介绍的是在沙漠铁路沿线使用草方格与高立式沙障进行生态防护和绿化带建设的技术，属于环境保护与生态治理范畴，而非建筑安全领域。内容未涉及建筑结构、施工安全、消防安全、电气安全等建筑相关安全问题，也未指出任何具体的安全隐患或需要整改的问题。虽然提到了工程措施，但其目的是防风固沙、保护铁路生态环境，不具备建筑安全隐患举报的特征。此外，内容更偏向科普性质，未提供可定位的具体地点，也无明确的当前安全隐患存在描述，不具备实地检查或整改的可操作性。
2025-07-30 18:21:47 - ExtractorAgent - INFO - [信息抽取] |■■■■■■■■■■■■■■■■■■□□| 90% (9/10)
2025-07-30 18:21:47 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 540
2025-07-30 18:21:47 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 18:21:52 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-30 18:21:52 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-07-30 18:21:52 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:52 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:52 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:52 - LiteLLM - INFO - selected model name for cost calculation: qwen-plus
2025-07-30 18:21:52 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'domain_relevance': 0.1, 'hazard_existence': 0.1, 'current_status': 0.1, 'specificity': 0.2, 'actionability': 0.1, 'content_nature': 0.1, 'overall_confidence': 0.1, 'content_type': 'non_construction', 'reason': '该微博内容主要介绍PE钢带波纹管在农田水利系统中的应用，强调其在输水效率、结构强度、排水性能及经济性方面的优势。内容属于产品宣传与技术推广，未描述任何建筑结构、施工、消防、电气等建筑安全领域的具体问题。虽然提到了一些工程案例（如海南橡胶园、东北农田水利项目），但这些描述是用于展示产品性能，而非指出当前存在的建筑安全隐患。没有具体建筑安全隐患的描述，也没有可采取实地整改措施的问题点。因此不属于建筑安全隐患举报内容。'}
2025-07-30 18:21:52 - LiteLLM - INFO - selected model name for cost calculation: openai/qwen-plus
2025-07-30 18:21:52 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - 该微博内容主要介绍PE钢带波纹管在农田水利系统中的应用，强调其在输水效率、结构强度、排水性能及经济性方面的优势。内容属于产品宣传与技术推广，未描述任何建筑结构、施工、消防、电气等建筑安全领域的具体问题。虽然提到了一些工程案例（如海南橡胶园、东北农田水利项目），但这些描述是用于展示产品性能，而非指出当前存在的建筑安全隐患。没有具体建筑安全隐患的描述，也没有可采取实地整改措施的问题点。因此不属于建筑安全隐患举报内容。
2025-07-30 18:21:52 - LiteLLM - INFO - selected model name for cost calculation: qwen-plus
2025-07-30 18:21:52 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: 该微博内容主要介绍PE钢带波纹管在农田水利系统中的应用，强调其在输水效率、结构强度、排水性能及经济性方面的优势。内容属于产品宣传与技术推广，未描述任何建筑结构、施工、消防、电气等建筑安全领域的具体问题。虽然提到了一些工程案例（如海南橡胶园、东北农田水利项目），但这些描述是用于展示产品性能，而非指出当前存在的建筑安全隐患。没有具体建筑安全隐患的描述，也没有可采取实地整改措施的问题点。因此不属于建筑安全隐患举报内容。
2025-07-30 18:21:52 - ExtractorAgent - INFO - [信息抽取] |■■■■■■■■■■■■■■■■■■■■| 100% (10/10)
2025-07-30 18:21:52 - ExtractorAgent - INFO - [信息抽取] |■■■■■■■■■■■■■■■■■■■■| 100% (10/10)
2025-07-30 18:21:52 - ExtractorAgent - INFO - [信息抽取] 完成！共处理 10 项
2025-07-30 18:21:52 - ExtractorAgent - INFO - 
信息提取完成统计:
- 处理微博数: 10
- 成功提取: 0 条隐患
- 跳过抽取: 10 条 (语义预判断)
- 低质量过滤: 0 条
- 错误失败: 0 条
- 成功率: 0.0%
- 处理耗时: 60.96 秒
- 平均耗时: 6.10 秒/条
        
2025-07-30 18:21:52 - ExtractorAgent - INFO - 结构化数据已保存到: D:\Users\Administrator\Desktop\3.01\sys3.0\data\structured\TASK-20250730-02.json
2025-07-30 18:21:52 - DataService - INFO - 已保存任务 TASK-20250730-02 的 structured 阶段数据到 D:\Users\Administrator\Desktop\3.01\sys3.0\data\structured\TASK-20250730-02.json
2025-07-30 18:21:52 - ExtractorAgent - INFO - 结构化数据已保存到任务: TASK-20250730-02
2025-07-30 18:21:52 - ExtractorAgent - WARNING - 未提取到任何隐患信息，但已保存空结果
2025-07-30 18:21:52 - ControllerAgent - INFO - 任务执行完成，耗时: 60.97秒
2025-07-30 18:21:52 - system - INFO - 任务执行完成，耗时: 60.98 秒
2025-07-30 18:21:52 - system - INFO - 
============================================================
2025-07-30 18:21:52 - system - INFO - = 任务执行成功
2025-07-30 18:21:52 - system - INFO - = 任务ID: TASK-20250730-02
2025-07-30 18:21:52 - system - INFO - = 执行时间: 60.98 秒
2025-07-30 18:21:52 - system - INFO - ============================================================

