2025-07-30 18:06:50 - numexpr.utils - INFO - Note: NumExpr detected 20 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 16.
2025-07-30 18:06:50 - numexpr.utils - INFO - NumExpr defaulting to 16 threads.
2025-07-30 18:06:53 - LawRAGEngine - INFO - 初始化法规检索引擎
2025-07-30 18:06:53 - LawRAGEngine - INFO - 法规RAG引擎使用设备: cpu
2025-07-30 18:06:53 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: D:\Users\Administrator\Desktop\3.01\sys3.0\models\paraphrase-multilingual-MiniLM-L12-v2
2025-07-30 18:06:54 - LawRAGEngine - INFO - 初始化重排序模型: D:\Users\Administrator\Desktop\3.01\sys3.0\models\bge-reranker-base
2025-07-30 18:06:54 - LawRAGEngine - INFO - 重排序模型初始化成功，使用设备: cpu
2025-07-30 18:06:54 - LawRAGEngine - INFO - 重排序模型预热完成
2025-07-30 18:06:54 - LawRAGEngine - INFO - 从 D:\Users\Administrator\Desktop\3.01\sys3.0\tools\law_rag_engine\vector 加载向量存储
2025-07-30 18:06:54 - LawRAGEngine - ERROR - 加载向量存储失败: Could not import faiss python package. Please install it with `pip install faiss-gpu` (for CUDA supported GPU) or `pip install faiss-cpu` (depending on Python version).
Traceback (most recent call last):
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 56, in dependable_faiss_import
    import faiss
ModuleNotFoundError: No module named 'faiss'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Users\Administrator\Desktop\3.01\sys3.0\tools\law_rag_engine\rag_engine.py", line 137, in __init__
    self.vectorstore = FAISS.load_local(
                       ~~~~~~~~~~~~~~~~^
        str(self.vector_store_path),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        self.embeddings,
        ^^^^^^^^^^^^^^^^
        allow_dangerous_deserialization=True  # 添加参数，允许反序列化
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 1204, in load_local
    faiss = dependable_faiss_import()
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 58, in dependable_faiss_import
    raise ImportError(
    ...<3 lines>...
    )
ImportError: Could not import faiss python package. Please install it with `pip install faiss-gpu` (for CUDA supported GPU) or `pip install faiss-cpu` (depending on Python version).
2025-07-30 18:06:54 - LawRAGEngine - INFO - 法规向量存储初始化完成
2025-07-30 18:06:55 - smart_graph_loader - INFO - 🚀 开始智能图谱数据检查和加载
2025-07-30 18:06:59 - smart_graph_loader - ERROR - ❌ 数据库连接失败: [4000] Neo4j服务不可用: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
2025-07-30 18:07:07 - opentelemetry.trace - WARNING - Overriding of current TracerProvider is not allowed
2025-07-30 18:07:08 - system - INFO - 系统信息: Python 3.13.5 | packaged by Anaconda, Inc. | (main, Jun 12 2025, 16:37:03) [MSC v.1929 64 bit (AMD64)], OS: Windows
2025-07-30 18:07:08 - system - INFO - CrewAI框架版本已加载，系统将使用CrewAI执行工作
2025-07-30 18:07:08 - system - INFO - 执行系统状态检查..
2025-07-30 18:07:08 - system - INFO - 
============================================================
2025-07-30 18:07:08 - system - INFO - = 系统运行中，将从网络收集数据
2025-07-30 18:07:08 - system - INFO - ============================================================

2025-07-30 18:07:08 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus, 超时: 60s, 重试: 3次
2025-07-30 18:07:12 - system - INFO - 系统状态检查结果:
2025-07-30 18:07:12 - system - INFO - - config: ok - 配置加载成功
2025-07-30 18:07:12 - system - INFO - - llm: ok - LLM配置有效，提供商: openai
2025-07-30 18:07:12 - system - INFO - - data_dirs: ok - 数据目录已准备就绪
2025-07-30 18:07:12 - system - ERROR - - neo4j: error - Neo4j连接失败: [4000] Neo4j服务不可用: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
2025-07-30 18:07:12 - system - WARNING - - gpu: warning - GPU不可用，使用CPU模式
2025-07-30 18:07:12 - system - ERROR - 
系统状态检查失败。以下组件存在问题:
- neo4j: Neo4j连接失败: [4000] Neo4j服务不可用: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
- gpu: GPU不可用，使用CPU模式
2025-07-30 18:07:24 - numexpr.utils - INFO - Note: NumExpr detected 20 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 16.
2025-07-30 18:07:24 - numexpr.utils - INFO - NumExpr defaulting to 16 threads.
2025-07-30 18:07:27 - LawRAGEngine - INFO - 初始化法规检索引擎
2025-07-30 18:07:27 - LawRAGEngine - INFO - 法规RAG引擎使用设备: cpu
2025-07-30 18:07:27 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: D:\Users\Administrator\Desktop\3.01\sys3.0\models\paraphrase-multilingual-MiniLM-L12-v2
2025-07-30 18:07:28 - LawRAGEngine - INFO - 初始化重排序模型: D:\Users\Administrator\Desktop\3.01\sys3.0\models\bge-reranker-base
2025-07-30 18:07:28 - LawRAGEngine - INFO - 重排序模型初始化成功，使用设备: cpu
2025-07-30 18:07:28 - LawRAGEngine - INFO - 重排序模型预热完成
2025-07-30 18:07:28 - LawRAGEngine - INFO - 从 D:\Users\Administrator\Desktop\3.01\sys3.0\tools\law_rag_engine\vector 加载向量存储
2025-07-30 18:07:28 - LawRAGEngine - ERROR - 加载向量存储失败: Could not import faiss python package. Please install it with `pip install faiss-gpu` (for CUDA supported GPU) or `pip install faiss-cpu` (depending on Python version).
Traceback (most recent call last):
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 56, in dependable_faiss_import
    import faiss
ModuleNotFoundError: No module named 'faiss'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Users\Administrator\Desktop\3.01\sys3.0\tools\law_rag_engine\rag_engine.py", line 137, in __init__
    self.vectorstore = FAISS.load_local(
                       ~~~~~~~~~~~~~~~~^
        str(self.vector_store_path),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        self.embeddings,
        ^^^^^^^^^^^^^^^^
        allow_dangerous_deserialization=True  # 添加参数，允许反序列化
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 1204, in load_local
    faiss = dependable_faiss_import()
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 58, in dependable_faiss_import
    raise ImportError(
    ...<3 lines>...
    )
ImportError: Could not import faiss python package. Please install it with `pip install faiss-gpu` (for CUDA supported GPU) or `pip install faiss-cpu` (depending on Python version).
2025-07-30 18:07:28 - LawRAGEngine - INFO - 法规向量存储初始化完成
2025-07-30 18:07:28 - smart_graph_loader - INFO - 🚀 开始智能图谱数据检查和加载
2025-07-30 18:07:32 - smart_graph_loader - ERROR - ❌ 数据库连接失败: [4000] Neo4j服务不可用: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
2025-07-30 18:07:36 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-07-30 18:07:37 - opentelemetry.trace - WARNING - Overriding of current TracerProvider is not allowed
2025-07-30 18:07:38 - system - ERROR - 
错误: 运行模式 'extract' 依赖于collect的输出，请提供输入文件
