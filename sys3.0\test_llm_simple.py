#!/usr/bin/env python
"""
简单的LLM调用测试
直接使用litellm测试API连接
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
ROOT_DIR = Path(__file__).parent
sys.path.insert(0, str(ROOT_DIR))

# 导入配置服务
from config.config import config_service

# 加载配置
config_service.load_config()

def test_litellm_direct():
    """直接使用litellm测试API调用"""
    print("=" * 50)
    print("直接测试LiteLLM API调用...")
    
    try:
        import litellm
        
        # 获取配置
        llm_config = config_service.get_config("llm", as_dict=True)
        
        print(f"配置信息:")
        print(f"  提供商: {llm_config.get('provider')}")
        print(f"  模型: {llm_config.get('model')}")
        print(f"  API Base: {llm_config.get('base_url')}")
        print(f"  超时: {llm_config.get('timeout')}秒")
        
        # 设置超时
        litellm.request_timeout = llm_config.get('timeout', 60)
        
        # 构建模型名称
        model_name = f"openai/{llm_config.get('model')}"
        
        print(f"\n开始调用API...")
        print(f"模型名称: {model_name}")
        
        # 简单的API调用
        response = litellm.completion(
            model=model_name,
            messages=[{"role": "user", "content": "1+1等于几？只回答数字。"}],
            api_key=llm_config.get('api_key'),
            base_url=llm_config.get('base_url'),
            timeout=llm_config.get('timeout', 60),
            max_tokens=100
        )
        
        print(f"✅ API调用成功!")
        print(f"响应: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ API调用失败: {str(e)}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False

def test_network_connection():
    """测试网络连接"""
    print("\n" + "=" * 50)
    print("测试网络连接...")
    
    try:
        import requests
        
        llm_config = config_service.get_config("llm", as_dict=True)
        base_url = llm_config.get('base_url')
        
        if not base_url:
            print("❌ 没有配置base_url")
            return False
            
        print(f"测试连接: {base_url}")
        
        # 测试基本连接
        response = requests.get(base_url, timeout=10)
        print(f"✅ 网络连接正常，状态码: {response.status_code}")
        return True
        
    except Exception as e:
        print(f"❌ 网络连接失败: {str(e)}")
        return False

def test_api_key():
    """测试API密钥"""
    print("\n" + "=" * 50)
    print("检查API密钥...")
    
    llm_config = config_service.get_config("llm", as_dict=True)
    api_key = llm_config.get('api_key')
    
    if not api_key:
        print("❌ API密钥未配置")
        return False
    
    if len(api_key) < 10:
        print("❌ API密钥太短，可能无效")
        return False
        
    print(f"✅ API密钥已配置 (长度: {len(api_key)})")
    print(f"   前缀: {api_key[:10]}...")
    return True

if __name__ == "__main__":
    print("LLM简单测试开始")
    print("=" * 50)
    
    # 测试API密钥
    key_ok = test_api_key()
    
    # 测试网络连接
    network_ok = test_network_connection()
    
    # 测试LiteLLM调用
    if key_ok and network_ok:
        api_ok = test_litellm_direct()
    else:
        api_ok = False
        print("\n跳过API测试，因为前置条件不满足")
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print(f"API密钥: {'✅ 正常' if key_ok else '❌ 异常'}")
    print(f"网络连接: {'✅ 正常' if network_ok else '❌ 异常'}")
    print(f"API调用: {'✅ 成功' if api_ok else '❌ 失败'}")
    
    if api_ok:
        print("\n🎉 LLM调用完全正常！")
    else:
        print("\n⚠️ LLM调用存在问题，请检查网络和配置。")
